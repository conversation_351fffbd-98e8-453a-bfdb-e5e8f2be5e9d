<?php

echo "🧪 Testing Recent Submission API\n";
echo "================================\n\n";

function testAPI($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode >= 200 && $httpCode < 300) {
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "✅ SUCCESS\n";
            if (isset($responseData['message'])) {
                echo "Message: " . $responseData['message'] . "\n";
            }
            if (isset($responseData['data'])) {
                echo "Data keys: " . implode(', ', array_keys($responseData['data'])) . "\n";
                
                if (isset($responseData['data']['recent_answers'])) {
                    echo "Recent answers count: " . count($responseData['data']['recent_answers']) . "\n";
                }
                
                if (isset($responseData['data']['statistics'])) {
                    $stats = $responseData['data']['statistics'];
                    echo "Statistics:\n";
                    echo "  - Total answers: " . ($stats['total_answers'] ?? 0) . "\n";
                    echo "  - Correct answers: " . ($stats['correct_answers'] ?? 0) . "\n";
                    echo "  - Accuracy: " . ($stats['accuracy_percentage'] ?? 0) . "%\n";
                    echo "  - Score: " . ($stats['score'] ?? 0) . "\n";
                    echo "  - Completed: " . ($stats['is_completed'] ? 'Yes' : 'No') . "\n";
                    echo "  - Attempts: " . ($stats['attempts_count'] ?? 0) . "\n";
                }
            }
        } else {
            echo "❌ FAILED - Invalid JSON\n";
        }
    } else {
        echo "❌ FAILED\n";
        echo "Response: " . substr($response, 0, 300) . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    return $httpCode >= 200 && $httpCode < 300;
}

$baseUrl = 'http://localhost:8000/api';

// Test với submission time từ 1 giờ trước
$submissionTime1 = date('Y-m-d H:i:s', strtotime('-1 hour'));
echo "=== Test 1: Recent submissions from 1 hour ago ===\n";
testAPI("$baseUrl/student-answers/recent-submission/student/1/course/9/lesson-part/1?submission_time=" . urlencode($submissionTime1), "Recent submissions from 1 hour ago");

// Test với submission time từ 10 phút trước
$submissionTime2 = date('Y-m-d H:i:s', strtotime('-10 minutes'));
echo "=== Test 2: Recent submissions from 10 minutes ago ===\n";
testAPI("$baseUrl/student-answers/recent-submission/student/1/course/9/lesson-part/1?submission_time=" . urlencode($submissionTime2), "Recent submissions from 10 minutes ago");

// Test với student 2 vừa submit
$submissionTime3 = date('Y-m-d H:i:s', strtotime('-5 minutes'));
echo "=== Test 3: Recent submissions for Student 2 ===\n";
testAPI("$baseUrl/student-answers/recent-submission/student/2/course/4/lesson-part/2?submission_time=" . urlencode($submissionTime3), "Recent submissions for Student 2");

// Test với submission time trong tương lai (không có data)
$submissionTime4 = date('Y-m-d H:i:s', strtotime('+1 hour'));
echo "=== Test 4: No submissions (future time) ===\n";
testAPI("$baseUrl/student-answers/recent-submission/student/1/course/9/lesson-part/1?submission_time=" . urlencode($submissionTime4), "No submissions (future time)");

echo "🏁 Testing completed!\n";
