<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Question;
use App\Models\Answer;
use App\Models\LessonPart;
use Illuminate\Support\Facades\DB;

class ComprehensiveQuestionsSeeder extends Seeder
{
    public function run(): void
    {
        // Xóa dữ liệu cũ
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Answer::truncate();
        Question::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $lessonParts = LessonPart::all();
        
        foreach ($lessonParts as $lessonPart) {
            $this->createQuestionsForLessonPart($lessonPart);
        }
    }

    private function createQuestionsForLessonPart($lessonPart)
    {
        $questionTypes = [
            'single_choice',
            'matching', 
            'classification',
            'fill_blank',
            'arrangement',
            'image_word'
        ];

        $questionIndex = 1;

        // Tạo 10 câu hỏi cho mỗi lesson part
        for ($i = 0; $i < 10; $i++) {
            $questionType = $questionTypes[$i % 6]; // Lặp qua 6 loại câu hỏi
            
            $questionData = $this->getQuestionData($questionType, $lessonPart->level, $questionIndex);
            
            $question = Question::create([
                'lesson_part_id' => $lessonPart->lesson_part_id,
                'question_type' => $questionType,
                'question_text' => $questionData['question_text'],
                'order_index' => $questionIndex,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Tạo answers cho câu hỏi
            foreach ($questionData['answers'] as $answerIndex => $answerData) {
                Answer::create([
                    'questions_id' => $question->questions_id,
                    'match_key' => 'key_' . $question->questions_id . '_' . ($answerIndex + 1),
                    'answer_text' => $answerData['text'],
                    'is_correct' => $answerData['correct'],
                    'order_index' => $answerData['order'] ?? ($answerIndex + 1),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            $questionIndex++;
        }
    }

    private function getQuestionData($type, $level, $index)
    {
        switch ($type) {
            case 'single_choice':
                return $this->getSingleChoiceQuestion($level, $index);
            case 'matching':
                return $this->getMatchingQuestion($level, $index);
            case 'classification':
                return $this->getClassificationQuestion($level, $index);
            case 'fill_blank':
                return $this->getFillBlankQuestion($level, $index);
            case 'arrangement':
                return $this->getArrangementQuestion($level, $index);
            case 'image_word':
                return $this->getImageWordQuestion($level, $index);
            default:
                return $this->getSingleChoiceQuestion($level, $index);
        }
    }

    private function getSingleChoiceQuestion($level, $index)
    {
        $questions = [
            'A1' => [
                "What is the correct translation of 'Hello'?",
                "Which word means 'book'?",
                "What is the past tense of 'go'?",
                "How do you say 'thank you'?",
                "What color is the sun?",
            ],
            'A2' => [
                "Which sentence is grammatically correct?",
                "What is the comparative form of 'good'?",
                "Choose the correct preposition: 'I go ___ school'",
                "What does 'beautiful' mean?",
                "Which is the correct question form?",
            ],
            'A3' => [
                "What is the correct passive voice form?",
                "Choose the appropriate modal verb",
                "What is the meaning of this idiom?",
                "Which conditional sentence is correct?",
                "What is the correct reported speech?",
            ],
            'TA 2/6' => [
                "Analyze the complex sentence structure",
                "Choose the most appropriate advanced vocabulary",
                "What is the correct academic writing style?",
                "Identify the literary device used",
                "What is the correct business English expression?",
            ]
        ];

        $questionTexts = $questions[$level] ?? $questions['A1'];
        $questionText = $questionTexts[($index - 1) % count($questionTexts)];

        return [
            'question_text' => $questionText,
            'answers' => [
                ['text' => 'Option A', 'correct' => true],
                ['text' => 'Option B', 'correct' => false],
                ['text' => 'Option C', 'correct' => false],
                ['text' => 'Option D', 'correct' => false],
            ]
        ];
    }

    private function getMatchingQuestion($level, $index)
    {
        return [
            'question_text' => "Match the English word with its Vietnamese meaning (Question $index)",
            'answers' => [
                ['text' => 'Apple - Táo', 'correct' => true],
                ['text' => 'Apple - Cam', 'correct' => false],
                ['text' => 'Apple - Chuối', 'correct' => false],
            ]
        ];
    }

    private function getClassificationQuestion($level, $index)
    {
        return [
            'question_text' => "Classify these words into correct categories (Question $index)",
            'answers' => [
                ['text' => 'run', 'correct' => true, 'order' => 1], // Verb
                ['text' => 'beautiful', 'correct' => true, 'order' => 2], // Adjective  
                ['text' => 'quickly', 'correct' => true, 'order' => 3], // Adverb
                ['text' => 'house', 'correct' => false, 'order' => 1], // Wrong category
            ]
        ];
    }

    private function getFillBlankQuestion($level, $index)
    {
        return [
            'question_text' => "Fill in the blank: 'I ___ to school every day' (Question $index)",
            'answers' => [
                ['text' => 'go', 'correct' => true],
            ]
        ];
    }

    private function getArrangementQuestion($level, $index)
    {
        return [
            'question_text' => "Arrange these words to make a correct sentence (Question $index)",
            'answers' => [
                ['text' => 'I', 'correct' => true, 'order' => 1],
                ['text' => 'am', 'correct' => true, 'order' => 2],
                ['text' => 'a', 'correct' => true, 'order' => 3],
                ['text' => 'student', 'correct' => true, 'order' => 4],
                ['text' => 'teacher', 'correct' => false, 'order' => 1], // Wrong word
            ]
        ];
    }

    private function getImageWordQuestion($level, $index)
    {
        return [
            'question_text' => "Look at the image and arrange letters to form the correct word (Question $index)",
            'answers' => [
                ['text' => 'c', 'correct' => true, 'order' => 1],
                ['text' => 'a', 'correct' => true, 'order' => 2], 
                ['text' => 't', 'correct' => true, 'order' => 3],
                ['text' => 'x', 'correct' => false, 'order' => 1], // Wrong letter
                ['text' => 'z', 'correct' => false, 'order' => 2], // Wrong letter
            ]
        ];
    }
}
