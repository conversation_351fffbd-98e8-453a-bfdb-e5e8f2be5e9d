<?php

echo "🧪 Testing Update Student Answers API\n";
echo "=====================================\n\n";

function testAPI($url, $description, $method = 'GET', $data = null) {
    echo "Testing: $description\n";
    echo "Method: $method\n";
    echo "URL: $url\n";
    if ($data) {
        echo "Data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json', 'Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    
    if ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode >= 200 && $httpCode < 300) {
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "✅ SUCCESS\n";
            if (isset($responseData['message'])) {
                echo "Message: " . $responseData['message'] . "\n";
            }
            if (isset($responseData['data'])) {
                echo "Response data keys: " . implode(', ', array_keys($responseData['data'])) . "\n";
                if (isset($responseData['data']['score'])) {
                    echo "Score: " . $responseData['data']['score'] . "\n";
                }
                if (isset($responseData['data']['completion_percentage'])) {
                    echo "Completion: " . $responseData['data']['completion_percentage'] . "%\n";
                }
            }
        } else {
            echo "❌ FAILED - Invalid JSON\n";
        }
    } else {
        echo "❌ FAILED\n";
        echo "Response: " . substr($response, 0, 500) . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    return $httpCode >= 200 && $httpCode < 300;
}

$baseUrl = 'http://localhost:8000/api';

// Test với questions thực tế từ lesson_part_id = 1
echo "=== Testing Update Student Answers ===\n";
testAPI(
    "$baseUrl/student-answers/student/1/course/9/lesson-part/1", 
    "Update Student Answers for Student 1, Course 9, Lesson Part 1",
    'PUT',
    [
        'answers' => [
            [
                'question_id' => 1,
                'answer_text' => 'Hello',
                'is_correct' => true
            ],
            [
                'question_id' => 2,
                'answer_text' => 'World',
                'is_correct' => false
            ]
        ]
    ]
);

// Test với questions khác
echo "=== Testing Update with different answers ===\n";
testAPI(
    "$baseUrl/student-answers/student/2/course/4/lesson-part/2", 
    "Update Student Answers for Student 2, Course 4, Lesson Part 2",
    'PUT',
    [
        'answers' => [
            [
                'question_id' => 11,
                'answer_text' => 'Test answer A'
            ],
            [
                'question_id' => 12,
                'answer_text' => 'Test answer B'
            ],
            [
                'question_id' => 13,
                'answer_text' => 'Test answer C'
            ]
        ]
    ]
);

echo "🏁 Testing completed!\n";
