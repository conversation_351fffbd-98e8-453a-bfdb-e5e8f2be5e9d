<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            // Drop old contents_id foreign key if exists
            if (Schema::hasColumn('questions', 'contents_id')) {
                $table->dropForeign(['contents_id']);
                $table->dropColumn('contents_id');
            }

            // Add lesson_part_id column (nullable first)
            if (!Schema::hasColumn('questions', 'lesson_part_id')) {
                $table->unsignedBigInteger('lesson_part_id')->nullable()->after('questions_id');
            }
        });

        // Note: Foreign key constraint will be added after seeding data
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            // Drop new foreign key and column
            $table->dropForeign(['lesson_part_id']);
            $table->dropColumn('lesson_part_id');

            // Restore old contents_id
            $table->unsignedBigInteger('contents_id')->after('questions_id');
            $table->foreign('contents_id')->references('contents_id')->on('lesson_part_contents')->onDelete('cascade');
        });
    }
};
