<?php

/**
 * Test Progress APIs with Updated Formula
 * Tests all progress-related endpoints to ensure they work correctly
 */

function testAPI($method, $url, $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'response' => $response ? json_decode($response, true) : null
    ];
}

function printTestResult($testName, $method, $url, $result, $data = null) {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "🧪 TESTING: $testName\n";
    echo "📍 $method $url\n";
    echo str_repeat("-", 80) . "\n";
    
    if ($data) {
        echo "📤 Request Data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
        echo str_repeat("-", 40) . "\n";
    }
    
    if ($result['status'] == 200) {
        echo "✅ Status: {$result['status']} (SUCCESS)\n";
        if (isset($result['response']['success'])) {
            echo "📊 Success: " . ($result['response']['success'] ? 'true' : 'false') . "\n";
        }
        if (isset($result['response']['data'])) {
            $data = $result['response']['data'];
            if (isset($data['progress_percentage'])) {
                echo "📈 Progress: {$data['progress_percentage']}%\n";
            }
            if (isset($data['overall_progress_percentage'])) {
                echo "📈 Overall Progress: {$data['overall_progress_percentage']}%\n";
            }
            if (isset($data['is_completed'])) {
                echo "🎯 Completed: " . ($data['is_completed'] ? 'Yes' : 'No') . "\n";
            }
            if (isset($data['total_questions'])) {
                echo "❓ Total Questions: {$data['total_questions']}\n";
            }
            if (isset($data['answered_questions'])) {
                echo "✍️ Answered: {$data['answered_questions']}\n";
            }
            if (isset($data['correct_answers'])) {
                echo "✅ Correct: {$data['correct_answers']}\n";
            }
        }
    } else {
        echo "❌ Status: {$result['status']} (ERROR)\n";
        if ($result['response']) {
            echo "💬 Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n";
        }
    }
}

echo "🔧 TESTING PROGRESS APIs WITH UPDATED FORMULA\n";
echo str_repeat("=", 50) . "\n";

$baseUrl = 'http://localhost:8000/api';

// Test 1: Course Progress
$result1 = testAPI('GET', "$baseUrl/progress/course/1/student/1");
printTestResult('Course Progress', 'GET', "$baseUrl/progress/course/1/student/1", $result1);

// Test 2: Lesson Part Progress
$result2 = testAPI('GET', "$baseUrl/progress/lesson-part/1/student/1");
printTestResult('Lesson Part Progress', 'GET', "$baseUrl/progress/lesson-part/1/student/1", $result2);

// Test 3: Lesson Progress (by level)
$result3 = testAPI('GET', "$baseUrl/progress/lesson/A1/student/1");
printTestResult('Lesson Progress by Level', 'GET', "$baseUrl/progress/lesson/A1/student/1", $result3);

// Test 4: Student Progress Overview
$result4 = testAPI('GET', "$baseUrl/progress/student/1/overview");
printTestResult('Student Progress Overview', 'GET', "$baseUrl/progress/student/1/overview", $result4);

// Test 5: Student Overall Progress
$result5 = testAPI('GET', "$baseUrl/progress/student/1/overall");
printTestResult('Student Overall Progress', 'GET', "$baseUrl/progress/student/1/overall", $result5);

// Test 6: Update Student Progress
$progressData = [
    'score_id' => 1,
    'completion_status' => true
];
$result6 = testAPI('POST', "$baseUrl/student-progress", $progressData);
printTestResult('Update Student Progress', 'POST', "$baseUrl/student-progress", $result6, $progressData);

// Test 7: Submit some answers to test progress calculation
echo "\n" . str_repeat("=", 80) . "\n";
echo "🎯 TESTING PROGRESS CALCULATION WITH NEW ANSWERS\n";
echo str_repeat("=", 80) . "\n";

// Submit correct answers
$answers = [
    ['student_id' => 1, 'questions_id' => 1, 'course_id' => 1, 'answer_text' => 'A'],
    ['student_id' => 1, 'questions_id' => 2, 'course_id' => 1, 'answer_text' => 'B'],
    ['student_id' => 1, 'questions_id' => 3, 'course_id' => 1, 'answer_text' => 'C'],
    ['student_id' => 1, 'questions_id' => 4, 'course_id' => 1, 'answer_text' => 'A'],
    ['student_id' => 1, 'questions_id' => 5, 'course_id' => 1, 'answer_text' => 'B']
];

foreach ($answers as $i => $answer) {
    $result = testAPI('POST', "$baseUrl/student-answers", $answer);
    echo "📝 Answer " . ($i + 1) . ": " . ($result['status'] == 200 ? "✅ Success" : "❌ Failed") . "\n";
}

// Test progress again after submitting answers
echo "\n" . str_repeat("-", 80) . "\n";
echo "🔄 RE-TESTING PROGRESS AFTER SUBMITTING ANSWERS\n";
echo str_repeat("-", 80) . "\n";

$result7 = testAPI('GET', "$baseUrl/progress/course/1/student/1");
printTestResult('Course Progress (After Answers)', 'GET', "$baseUrl/progress/course/1/student/1", $result7);

$result8 = testAPI('GET', "$baseUrl/progress/lesson-part/1/student/1");
printTestResult('Lesson Part Progress (After Answers)', 'GET', "$baseUrl/progress/lesson-part/1/student/1", $result8);

echo "\n" . str_repeat("=", 80) . "\n";
echo "🏁 PROGRESS API TESTING COMPLETE\n";
echo str_repeat("=", 80) . "\n";

?>
