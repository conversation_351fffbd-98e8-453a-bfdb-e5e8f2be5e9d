<?php

/**
 * Simple test script to verify the progress calculation API
 * Run this with: php test_progress_api.php
 */

// Configuration
$baseUrl = 'http://localhost:8000/api';
$studentId = 1;
$courseId = 1;
$lessonPartId = 1;

function makeRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "🧪 Testing Progress Calculation API\n";
echo "=====================================\n\n";

// Test 1: Course Progress
echo "1️⃣ Testing Course Progress API\n";
$url = "$baseUrl/progress/course/$courseId/student/$studentId";
echo "URL: $url\n";

$result = makeRequest($url);
echo "Status: {$result['status']}\n";

if ($result['status'] === 200 && $result['body']['success']) {
    $data = $result['body']['data'];
    echo "✅ SUCCESS\n";
    echo "   - Course: {$data['course_name']} ({$data['course_level']})\n";
    echo "   - Total Questions: {$data['total_questions']}\n";
    echo "   - Answered: {$data['answered_questions']}\n";
    echo "   - Correct: {$data['correct_answers']}\n";
    echo "   - Progress: {$data['progress_percentage']}%\n";
    echo "   - Correct Rate: {$data['correct_percentage']}%\n";
    echo "   - Completed: " . ($data['is_completed'] ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ FAILED\n";
    if (isset($result['body']['message'])) {
        echo "   Error: {$result['body']['message']}\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Lesson Part Progress with Course Context
echo "2️⃣ Testing Lesson Part Progress API (with course context)\n";
$url = "$baseUrl/progress/lesson-part/$lessonPartId/student/$studentId/course/$courseId";
echo "URL: $url\n";

$result = makeRequest($url);
echo "Status: {$result['status']}\n";

if ($result['status'] === 200 && $result['body']['success']) {
    $data = $result['body']['data'];
    echo "✅ SUCCESS\n";
    echo "   - Lesson Part: {$data['lesson_part_title']}\n";
    echo "   - Total Questions: {$data['total_questions']}\n";
    echo "   - Answered: {$data['answered_questions']}\n";
    echo "   - Correct: {$data['correct_answers']}\n";
    echo "   - Progress: {$data['progress_percentage']}%\n";
    echo "   - Completed: " . ($data['is_completed'] ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ FAILED\n";
    if (isset($result['body']['message'])) {
        echo "   Error: {$result['body']['message']}\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Student Overall Progress
echo "3️⃣ Testing Student Overall Progress API\n";
$url = "$baseUrl/progress/student/$studentId/overview";
echo "URL: $url\n";

$result = makeRequest($url);
echo "Status: {$result['status']}\n";

if ($result['status'] === 200 && $result['body']['success']) {
    $data = $result['body']['data'];
    echo "✅ SUCCESS\n";
    echo "   - Student: {$data['student_name']}\n";
    echo "   - Total Courses: {$data['total_courses']}\n";
    echo "   - Completed Courses: {$data['completed_courses']}\n";
    echo "   - Overall Progress: {$data['overall_progress_percentage']}%\n";
    echo "   - Courses:\n";
    foreach ($data['courses_progress'] as $course) {
        echo "     * {$course['course_name']}: {$course['progress_percentage']}%\n";
    }
} else {
    echo "❌ FAILED\n";
    if (isset($result['body']['message'])) {
        echo "   Error: {$result['body']['message']}\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: Detailed Course Progress
echo "4️⃣ Testing Detailed Course Progress API\n";
$url = "$baseUrl/progress/course/$courseId/student/$studentId/detailed";
echo "URL: $url\n";

$result = makeRequest($url);
echo "Status: {$result['status']}\n";

if ($result['status'] === 200 && $result['body']['success']) {
    $data = $result['body']['data'];
    echo "✅ SUCCESS\n";
    echo "   - Course: {$data['course_name']}\n";
    echo "   - Overall Progress: {$data['overall_progress_percentage']}%\n";
    echo "   - Correct Rate: {$data['correct_percentage']}%\n";
    echo "   - Completed: " . ($data['is_completed'] ? 'Yes' : 'No') . "\n";
    echo "   - Lesson Parts:\n";
    foreach ($data['lessons_progress'] as $lesson) {
        echo "     * {$lesson['lesson_title']}: {$lesson['progress_percentage']}% ";
        echo "({$lesson['answered_questions']}/{$lesson['total_questions']} answered, ";
        echo "{$lesson['correct_answers']} correct)\n";
    }
} else {
    echo "❌ FAILED\n";
    if (isset($result['body']['message'])) {
        echo "   Error: {$result['body']['message']}\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🏁 Test completed!\n";
echo "\n📝 Notes:\n";
echo "- Make sure your Laravel server is running on http://localhost:8000\n";
echo "- Ensure you have test data with student_id=$studentId, course_id=$courseId\n";
echo "- Check database for student_answers and course_enrollments data\n";
echo "\n💡 To customize test parameters, edit the variables at the top of this script.\n";
?>
