<?php

use Illuminate\Support\Facades\DB;

/**
 * Database structure checker for Progress Calculation API
 * Run this with: php artisan tinker < check_database_structure.php
 */

echo "🔍 Checking Database Structure for Progress Calculation\n";
echo "=====================================================\n\n";

// Check if required tables exist
$tables = [
    'courses',
    'lessons', 
    'lesson_parts',
    'lesson_part_contents',
    'questions',
    'answers',
    'student_answers',
    'lesson_part_scores',
    'course_enrollments',
    'students'
];

echo "1️⃣ Checking Required Tables:\n";
foreach ($tables as $table) {
    try {
        $count = DB::table($table)->count();
        echo "   ✅ $table: $count records\n";
    } catch (Exception $e) {
        echo "   ❌ $table: Missing or error - {$e->getMessage()}\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Check relationships
echo "2️⃣ Checking Key Relationships:\n";

try {
    // Check course -> lesson relationship
    $coursesWithLessons = DB::table('courses')
        ->join('lessons', 'courses.level', '=', 'lessons.level')
        ->count();
    echo "   ✅ Courses with Lessons: $coursesWithLessons\n";
} catch (Exception $e) {
    echo "   ❌ Course-Lesson relationship: {$e->getMessage()}\n";
}

try {
    // Check lesson -> lesson_parts relationship
    $lessonsWithParts = DB::table('lessons')
        ->join('lesson_parts', 'lessons.level', '=', 'lesson_parts.level')
        ->count();
    echo "   ✅ Lessons with Parts: $lessonsWithParts\n";
} catch (Exception $e) {
    echo "   ❌ Lesson-LessonPart relationship: {$e->getMessage()}\n";
}

try {
    // Check lesson_parts -> questions relationship
    $partsWithQuestions = DB::table('lesson_parts')
        ->join('lesson_part_contents', 'lesson_parts.lesson_part_id', '=', 'lesson_part_contents.lesson_part_id')
        ->join('questions', 'lesson_part_contents.contents_id', '=', 'questions.contents_id')
        ->count();
    echo "   ✅ Lesson Parts with Questions: $partsWithQuestions\n";
} catch (Exception $e) {
    echo "   ❌ LessonPart-Question relationship: {$e->getMessage()}\n";
}

try {
    // Check questions -> answers relationship
    $questionsWithAnswers = DB::table('questions')
        ->join('answers', 'questions.questions_id', '=', 'answers.questions_id')
        ->count();
    echo "   ✅ Questions with Answers: $questionsWithAnswers\n";
} catch (Exception $e) {
    echo "   ❌ Question-Answer relationship: {$e->getMessage()}\n";
}

try {
    // Check student_answers with course_id
    $studentAnswersWithCourse = DB::table('student_answers')
        ->whereNotNull('course_id')
        ->count();
    echo "   ✅ Student Answers with Course ID: $studentAnswersWithCourse\n";
} catch (Exception $e) {
    echo "   ❌ Student Answers course_id: {$e->getMessage()}\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Check sample data for testing
echo "3️⃣ Sample Data Check:\n";

try {
    $sampleStudent = DB::table('students')->first();
    if ($sampleStudent) {
        echo "   ✅ Sample Student: ID {$sampleStudent->student_id} - {$sampleStudent->fullname}\n";
        
        // Check enrollments for this student
        $enrollments = DB::table('course_enrollments')
            ->where('student_id', $sampleStudent->student_id)
            ->count();
        echo "   ✅ Student Enrollments: $enrollments\n";
        
        // Check answers for this student
        $answers = DB::table('student_answers')
            ->where('student_id', $sampleStudent->student_id)
            ->count();
        echo "   ✅ Student Answers: $answers\n";
    } else {
        echo "   ❌ No students found in database\n";
    }
} catch (Exception $e) {
    echo "   ❌ Sample data check failed: {$e->getMessage()}\n";
}

try {
    $sampleCourse = DB::table('courses')->first();
    if ($sampleCourse) {
        echo "   ✅ Sample Course: ID {$sampleCourse->course_id} - {$sampleCourse->course_name} (Level: {$sampleCourse->level})\n";
        
        // Check if this course level has lesson parts
        $lessonParts = DB::table('lesson_parts')
            ->where('level', $sampleCourse->level)
            ->count();
        echo "   ✅ Course Lesson Parts: $lessonParts\n";
    } else {
        echo "   ❌ No courses found in database\n";
    }
} catch (Exception $e) {
    echo "   ❌ Sample course check failed: {$e->getMessage()}\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test the actual progress calculation query
echo "4️⃣ Testing Progress Calculation Query:\n";

try {
    if (isset($sampleStudent) && isset($sampleCourse) && $sampleStudent && $sampleCourse) {
        $studentId = $sampleStudent->student_id;
        $courseId = $sampleCourse->course_id;
        $level = $sampleCourse->level;
        
        echo "   Testing with Student ID: $studentId, Course ID: $courseId, Level: $level\n";
        
        $progressData = DB::select("
            SELECT 
                COALESCE((SUM(answered_count * question_count) / NULLIF(SUM(question_count * question_count), 0)) * 100, 0) AS course_progress,
                SUM(question_count) as total_questions,
                SUM(answered_count) as total_answered,
                SUM(correct_count) as total_correct
            FROM (
                SELECT 
                    lpc.lesson_part_id,
                    COUNT(DISTINCT q.questions_id) AS question_count,
                    COUNT(DISTINCT CASE WHEN sa.questions_id IS NOT NULL THEN sa.questions_id END) AS answered_count,
                    COUNT(DISTINCT CASE WHEN a.is_correct = 1 AND sa.answer_text = a.answer_text THEN sa.questions_id END) AS correct_count
                FROM lesson_part_contents lpc
                JOIN questions q ON lpc.contents_id = q.contents_id  
                JOIN lesson_parts lp ON lpc.lesson_part_id = lp.lesson_part_id
                LEFT JOIN student_answers sa ON q.questions_id = sa.questions_id AND sa.student_id = ? AND sa.course_id = ?
                LEFT JOIN answers a ON q.questions_id = a.questions_id AND a.is_correct = 1
                WHERE lp.level = ?
                GROUP BY lpc.lesson_part_id
            ) AS progress_table
        ", [$studentId, $courseId, $level]);
        
        if (!empty($progressData)) {
            $data = $progressData[0];
            echo "   ✅ Query executed successfully!\n";
            echo "   📊 Results:\n";
            echo "      - Total Questions: {$data->total_questions}\n";
            echo "      - Answered Questions: {$data->total_answered}\n";
            echo "      - Correct Answers: {$data->total_correct}\n";
            echo "      - Progress: " . round($data->course_progress, 2) . "%\n";
        } else {
            echo "   ⚠️ Query returned no results (this might be normal if no data exists)\n";
        }
    } else {
        echo "   ⚠️ Skipping query test - no sample data available\n";
    }
} catch (Exception $e) {
    echo "   ❌ Progress calculation query failed: {$e->getMessage()}\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🏁 Database structure check completed!\n\n";

echo "📝 Recommendations:\n";
echo "1. Ensure all tables have sample data for testing\n";
echo "2. Verify student_answers table has course_id populated\n";
echo "3. Check that answers table has is_correct field properly set\n";
echo "4. Make sure course_enrollments exist for test students\n";
echo "5. Run: php artisan db:seed to populate test data if needed\n";
