<?php

echo "🚀 Comprehensive API Testing with Reduced Dataset\n";
echo "=================================================\n\n";

function testAPI($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ SUCCESS\n";
            if (isset($data['data'])) {
                $count = is_array($data['data']) ? count($data['data']) : 'single object';
                echo "Data count: $count\n";
            }
        } else {
            echo "❌ FAILED - Invalid JSON\n";
        }
    } else {
        echo "❌ FAILED\n";
        echo "Response: " . substr($response, 0, 200) . "\n";
    }
    
    echo "\n";
    return $httpCode == 200;
}

function testPOSTAPI($url, $data, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode == 200 || $httpCode == 201) {
        echo "✅ SUCCESS\n";
    } else {
        echo "❌ FAILED\n";
        echo "Response: " . substr($response, 0, 200) . "\n";
    }
    
    echo "\n";
    return $httpCode == 200 || $httpCode == 201;
}

$baseUrl = 'http://localhost:8000/api';

echo "=== PROGRESS APIs ===\n";
testAPI("$baseUrl/progress/student/1/overview", "Student Overview Progress");
testAPI("$baseUrl/progress/course/9/student/1", "Course Progress for Student");
testAPI("$baseUrl/progress/lesson-part/1/student/1", "Lesson Part Progress");

echo "=== COURSE APIs ===\n";
testAPI("$baseUrl/courses", "All Courses");
testAPI("$baseUrl/courses/student/1", "Courses by Student");
testAPI("$baseUrl/courses/9/lesson-parts?student_id=1", "Lesson Parts with Progress");

echo "=== ENROLLMENT APIs ===\n";
testAPI("$baseUrl/enrollments/student/1", "Student Enrollments");
testAPI("$baseUrl/courses/9/students/count", "Course Student Count");

echo "=== SCORE APIs ===\n";
testAPI("$baseUrl/scores/student/1", "Student Scores");

echo "=== QUESTION APIs ===\n";
testAPI("$baseUrl/questions/lesson-part/1", "Questions by Lesson Part");

echo "=== POST APIs ===\n";
testPOSTAPI("$baseUrl/student-progress", [
    'score_id' => 1,
    'completion_status' => true
], "Update Student Progress");

echo "🏁 Comprehensive testing completed!\n";
