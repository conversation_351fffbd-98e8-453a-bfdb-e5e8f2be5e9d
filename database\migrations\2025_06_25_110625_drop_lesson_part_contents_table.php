<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('lesson_part_contents');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('lesson_part_contents', function (Blueprint $table) {
            $table->id('contents_id');
            $table->unsignedBigInteger('lesson_part_id');
            $table->string('content');
            $table->timestamps();

            $table->foreign('lesson_part_id')->references('lesson_part_id')->on('lesson_parts')->onDelete('cascade');
        });
    }
};
