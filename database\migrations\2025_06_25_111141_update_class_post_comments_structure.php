<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('class_post_comments', function (Blueprint $table) {
            // Check if columns exist before dropping
            if (Schema::hasColumn('class_post_comments', 'author_id')) {
                $table->dropColumn('author_id');
            }
            if (Schema::hasColumn('class_post_comments', 'author_type')) {
                $table->dropColumn('author_type');
            }

            // Add new foreign key columns only if they don't exist
            if (!Schema::hasColumn('class_post_comments', 'student_id')) {
                $table->unsignedBigInteger('student_id')->nullable()->after('post_id');
            }
            if (!Schema::hasColumn('class_post_comments', 'teacher_id')) {
                $table->unsignedBigInteger('teacher_id')->nullable()->after('student_id');
            }
        });

        // Add foreign key constraints separately
        Schema::table('class_post_comments', function (Blueprint $table) {
            if (!Schema::hasColumn('class_post_comments', 'student_id')) {
                $table->foreign('student_id')->references('student_id')->on('students')->onDelete('cascade');
            }
            if (!Schema::hasColumn('class_post_comments', 'teacher_id')) {
                $table->foreign('teacher_id')->references('teacher_id')->on('teachers')->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('class_post_comments', function (Blueprint $table) {
            // Drop foreign keys and new columns
            if (Schema::hasColumn('class_post_comments', 'student_id')) {
                $table->dropForeign(['student_id']);
                $table->dropColumn('student_id');
            }
            if (Schema::hasColumn('class_post_comments', 'teacher_id')) {
                $table->dropForeign(['teacher_id']);
                $table->dropColumn('teacher_id');
            }

            // Restore old polymorphic columns
            $table->unsignedBigInteger('author_id')->after('post_id');
            $table->string('author_type')->after('author_id');
        });
    }
};
