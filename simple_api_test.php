<?php

echo "🚀 Testing Reduced Dataset APIs\n";
echo "================================\n\n";

function testAPI($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ SUCCESS\n";
            if (isset($data['data'])) {
                echo "Data count: " . (is_array($data['data']) ? count($data['data']) : 'single object') . "\n";
            }
        } else {
            echo "❌ FAILED - Invalid JSON\n";
        }
    } else {
        echo "❌ FAILED\n";
        echo "Response: $response\n";
    }
    
    echo "\n";
    return $httpCode == 200;
}

$baseUrl = 'http://localhost:8000/api';

// Test key APIs
testAPI("$baseUrl/progress/student/1/overview", "Student Overview Progress");
testAPI("$baseUrl/progress/course/9/student/1", "Course Progress for Student (Course 9)");
testAPI("$baseUrl/courses/student/1", "Courses by Student");
testAPI("$baseUrl/enrollments/student/1", "Student Enrollments");
testAPI("$baseUrl/scores/student/1", "Student Scores");

echo "🏁 Testing completed!\n";
