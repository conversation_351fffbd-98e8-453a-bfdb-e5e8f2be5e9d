<?php

namespace App\Enum;

enum enrollment: int
{
    // chờ xác nhận: 1, đang học: 2, đ<PERSON><PERSON>: 3, không đạt: 4
    case verifying = 1;
    case studying = 2;
    case pass = 3;
    case fail = 4;

    public function getEnrollment(): string
    {
        return match ($this) {
            self::verifying => 'Chờ xác nhận',
            self::studying => 'Đang học',
            self::pass => 'Đạt',
            self::fail => 'Không đạt',
        };
    }

    public function getStatus(): string
    {
        return match ($this) {
            self::verifying => 'card-status-verifying',
            self::studying => 'card-status-studying',
            self::pass => 'card-status-pass',
            self::fail => 'card-status-fail',
        };
    }
}
