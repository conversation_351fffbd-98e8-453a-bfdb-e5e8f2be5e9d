<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('class_posts', function (Blueprint $table) {
            // Check if columns exist before dropping
            if (Schema::hasColumn('class_posts', 'author_id')) {
                $table->dropColumn('author_id');
            }
            if (Schema::hasColumn('class_posts', 'author_type')) {
                $table->dropColumn('author_type');
            }

            // Add teacher_id foreign key (nullable first)
            if (!Schema::hasColumn('class_posts', 'teacher_id')) {
                $table->unsignedBigInteger('teacher_id')->nullable()->after('course_id');
            }
        });

        // Note: Foreign key constraint will be added after seeding data
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('class_posts', function (Blueprint $table) {
            // Drop new column (check if foreign key exists first)
            if (Schema::hasColumn('class_posts', 'teacher_id')) {
                // Try to drop foreign key if it exists
                try {
                    $table->dropForeign(['teacher_id']);
                } catch (\Exception $e) {
                    // Foreign key might not exist, continue
                }
                $table->dropColumn('teacher_id');
            }

            // Restore old polymorphic columns
            if (!Schema::hasColumn('class_posts', 'author_id')) {
                $table->unsignedBigInteger('author_id')->after('course_id');
            }
            if (!Schema::hasColumn('class_posts', 'author_type')) {
                $table->string('author_type')->after('author_id');
            }
        });
    }
};
