<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Teacher;

class TeacherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Teacher::create([
            'fullname' => '<PERSON>',
            'username' => 'johnsmith',
            'password' => bcrypt('123456'),
            'date_of_birth' => '1985-07-10',
            'gender' => 1,
            'email' => '<EMAIL>',
            'is_status' => 1
        ]);
        Teacher::create([
            'fullname' => '<PERSON> nhut',
            'username' => 'gvnhut',
            'password' => bcrypt('123456'),
            'date_of_birth' => '1990-03-22',
            'gender' => 0,
            'email' => '<EMAIL>',
            'is_status' => 1
        ]);

        Teacher::create([
            'fullname' => '<PERSON>',
            'username' => 'm<PERSON><PERSON><PERSON>',
            'password' => bcrypt('123456'),
            'date_of_birth' => '1988-11-15',
            'gender' => 1,
            'email' => '<EMAIL>',
            'is_status' => 1
        ]);

        Teacher::create([
            'fullname' => '<PERSON> <PERSON>',
            'username' => 'swilson',
            'password' => bcrypt('123456'),
            'date_of_birth' => '1992-06-30',
            'gender' => 0,
            'email' => '<EMAIL>',
            'is_status' => 1
        ]);

        Teacher::create([
            'fullname' => 'David Brown',
            'username' => 'dbrown',
            'password' => bcrypt('123456'),
            'date_of_birth' => '1987-04-12',
            'gender' => 1,
            'email' => '<EMAIL>',
            'is_status' => 1
        ]);
    }
}
