{"info": {"name": "Progress Calculation API - Updated Formula", "description": "API collection for testing the updated progress calculation system using course_id based formula", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Course Progress", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/course/{{course_id}}/student/{{student_id}}", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "{{course_id}}", "student", "{{student_id}}"]}, "description": "Get course progress for a student using the CORRECT formula"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/course/1/student/1", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "1", "student", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"student_id\": 1,\n        \"course_id\": 1,\n        \"course_name\": \"English A1\",\n        \"course_level\": \"A1\",\n        \"total_questions\": 100,\n        \"answered_questions\": 75,\n        \"correct_answers\": 60,\n        \"progress_percentage\": 75.0,\n        \"correct_percentage\": 60.0,\n        \"is_completed\": false,\n        \"required_correct_percentage\": 70\n    }\n}"}]}, {"name": "Lesson Part Progress (with Course Context)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/lesson-part/{{lesson_part_id}}/student/{{student_id}}/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["api", "progress", "lesson-part", "{{lesson_part_id}}", "student", "{{student_id}}", "course", "{{course_id}}"]}, "description": "Get lesson part progress for a student within a specific course context"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/lesson-part/1/student/1/course/1", "host": ["{{base_url}}"], "path": ["api", "progress", "lesson-part", "1", "student", "1", "course", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"student_id\": 1,\n        \"lesson_part_id\": 1,\n        \"course_id\": 1,\n        \"lesson_part_title\": \"Grammar\",\n        \"total_questions\": 10,\n        \"answered_questions\": 8,\n        \"correct_answers\": 7,\n        \"progress_percentage\": 80.0,\n        \"is_completed\": true,\n        \"required_correct_answers\": 7\n    }\n}"}]}, {"name": "Student Overall Progress", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/student/{{student_id}}/overview", "host": ["{{base_url}}"], "path": ["api", "progress", "student", "{{student_id}}", "overview"]}, "description": "Get overall progress for a student across all enrolled courses"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/student/1/overview", "host": ["{{base_url}}"], "path": ["api", "progress", "student", "1", "overview"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"student_id\": 1,\n        \"student_name\": \"<PERSON>\",\n        \"total_courses\": 3,\n        \"completed_courses\": 1,\n        \"overall_progress_percentage\": 65.5,\n        \"courses_progress\": [\n            {\n                \"student_id\": 1,\n                \"course_id\": 1,\n                \"course_name\": \"English A1\",\n                \"course_level\": \"A1\",\n                \"total_questions\": 100,\n                \"answered_questions\": 75,\n                \"correct_answers\": 60,\n                \"progress_percentage\": 75.0,\n                \"correct_percentage\": 60.0,\n                \"is_completed\": false,\n                \"required_correct_percentage\": 70\n            }\n        ]\n    }\n}"}]}, {"name": "Detailed Course Progress (StudentProgressController)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/course/{{course_id}}/student/{{student_id}}/detailed", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "{{course_id}}", "student", "{{student_id}}", "detailed"]}, "description": "Get detailed course progress with lesson part breakdown"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/progress/course/1/student/1/detailed", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "1", "student", "1", "detailed"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"course_id\": 1,\n        \"course_name\": \"English A1\",\n        \"student_id\": 1,\n        \"enrollment_status\": 2,\n        \"total_questions\": 100,\n        \"answered_questions\": 75,\n        \"correct_answers\": 60,\n        \"overall_progress_percentage\": 75.0,\n        \"correct_percentage\": 60.0,\n        \"is_completed\": false,\n        \"required_correct_percentage\": 70,\n        \"lessons_progress\": [\n            {\n                \"lesson_part_id\": 1,\n                \"level\": \"A1\",\n                \"lesson_title\": \"Grammar\",\n                \"total_questions\": 10,\n                \"answered_questions\": 8,\n                \"correct_answers\": 7,\n                \"progress_percentage\": 80.0,\n                \"is_completed\": true\n            }\n        ],\n        \"total_time_spent_minutes\": 0,\n        \"estimated_completion_date\": \"2024-07-15\"\n    }\n}"}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "student_id", "value": "1", "type": "string"}, {"key": "course_id", "value": "1", "type": "string"}, {"key": "lesson_part_id", "value": "1", "type": "string"}]}