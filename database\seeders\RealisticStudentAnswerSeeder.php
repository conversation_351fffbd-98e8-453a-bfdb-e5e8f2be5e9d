<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\StudentAnswer;
use App\Models\Student;
use App\Models\Question;
use App\Models\Answer;
use App\Models\CourseEnrollment;
use App\Models\LessonPart;
use Carbon\Carbon;

class RealisticStudentAnswerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Xóa dữ liệu cũ
        StudentAnswer::truncate();
        
        $enrollments = CourseEnrollment::with(['student', 'course'])->get();

        foreach ($enrollments as $enrollment) {
            $this->createAnswersForEnrollment($enrollment);
        }
    }

    private function createAnswersForEnrollment($enrollment)
    {
        // Logic dựa trên status của enrollment
        switch ($enrollment->status) {
            case 1: // Pending - không có answers
                break;
            case 2: // Studying - có một số answers, chưa hoàn thành
                $this->createStudyingAnswers($enrollment);
                break;
            case 3: // Passed - có đầy đủ answers với tỷ lệ đúng cao
                $this->createPassedAnswers($enrollment);
                break;
            case 4: // Failed - có đầy đủ answers nhưng tỷ lệ đúng thấp
                $this->createFailedAnswers($enrollment);
                break;
        }
    }

    private function createStudyingAnswers($enrollment)
    {
        $lessonParts = LessonPart::where('level', $enrollment->course->level)->get();
        
        foreach ($lessonParts as $lessonPart) {
            $questions = Question::where('lesson_part_id', $lessonPart->lesson_part_id)->get();
            
            // Studying students: 40-80% câu hỏi đã trả lời
            $answerPercentage = rand(40, 80) / 100;
            $questionsToAnswer = $questions->take(ceil($questions->count() * $answerPercentage));
            
            foreach ($questionsToAnswer as $question) {
                $this->createStudentAnswer($enrollment, $question, 0.65); // 65% correct rate
            }
        }
    }

    private function createPassedAnswers($enrollment)
    {
        $lessonParts = LessonPart::where('level', $enrollment->course->level)->get();
        
        foreach ($lessonParts as $lessonPart) {
            $questions = Question::where('lesson_part_id', $lessonPart->lesson_part_id)->get();
            
            // Passed students: 100% câu hỏi đã trả lời
            foreach ($questions as $question) {
                $this->createStudentAnswer($enrollment, $question, 0.85); // 85% correct rate
            }
        }
    }

    private function createFailedAnswers($enrollment)
    {
        $lessonParts = LessonPart::where('level', $enrollment->course->level)->get();
        
        foreach ($lessonParts as $lessonPart) {
            $questions = Question::where('lesson_part_id', $lessonPart->lesson_part_id)->get();
            
            // Failed students: 100% câu hỏi đã trả lời nhưng tỷ lệ đúng thấp
            foreach ($questions as $question) {
                $this->createStudentAnswer($enrollment, $question, 0.45); // 45% correct rate
            }
        }
    }

    private function createStudentAnswer($enrollment, $question, $correctRate)
    {
        $answers = Answer::where('questions_id', $question->questions_id)->get();
        $correctAnswers = $answers->where('is_correct', true);
        $incorrectAnswers = $answers->where('is_correct', false);
        
        // Determine if answer should be correct based on correctRate
        $shouldBeCorrect = (rand(1, 100) / 100) <= $correctRate;
        
        if ($shouldBeCorrect && $correctAnswers->isNotEmpty()) {
            $selectedAnswer = $correctAnswers->random();
        } elseif ($incorrectAnswers->isNotEmpty()) {
            $selectedAnswer = $incorrectAnswers->random();
        } else {
            // Fallback to any answer
            $selectedAnswer = $answers->random();
        }

        // Create multiple attempts for some questions (realistic scenario)
        $attempts = rand(1, 2); // 1-2 attempts per question
        for ($i = 0; $i < $attempts; $i++) {
            $answeredAt = $this->getAnsweredAtTime($enrollment, $i);
            
            StudentAnswer::create([
                'student_id' => $enrollment->student_id,
                'questions_id' => $question->questions_id,
                'course_id' => $enrollment->assigned_course_id,
                'answer_text' => $selectedAnswer->answer_text,
                'answered_at' => $answeredAt,
            ]);
            
            // If first attempt was wrong, make subsequent attempts more likely to be correct
            if ($i == 0 && !$selectedAnswer->is_correct && $correctAnswers->isNotEmpty()) {
                $selectedAnswer = rand(1, 100) <= 70 ? $correctAnswers->random() : $selectedAnswer;
            }
        }
    }

    private function getAnsweredAtTime($enrollment, $attemptNumber)
    {
        $baseTime = Carbon::parse($enrollment->registration_date)->addDays(rand(1, 30));
        return $baseTime->addHours($attemptNumber * rand(1, 24));
    }
}
