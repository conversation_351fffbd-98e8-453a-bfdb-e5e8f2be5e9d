<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    protected $fillable = [
        'question_id',
        'lesson_part_id',
        'question_text',
        'question_type',
        'media_url',
        'order_index',
    ];
    protected $primaryKey = 'questions_id';
    //định nghĩa các quan hệ với các model khác
    public function lessonPart()
    {
        return $this->belongsTo(LessonPart::class, 'lesson_part_id', 'lesson_part_id');
    }
    public function Answers()
    {
        return $this->hasMany(Answer::class, 'questions_id', 'questions_id');
    }
    public function StudentAnswers()
    {
        return $this->hasMany(StudentAnswer::class, 'questions_id', 'questions_id');
    }
}
