<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherCourseAssignment extends Model
{
    protected $fillable = [
        'assignment_id',
        'teacher_id',
        'course_id',
        'role',
        'assigned_at',
    ];
    protected $primaryKey = 'assignment_id';
    //đ<PERSON><PERSON> nghĩa các quan hệ với các model khác
    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'teacher_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }
}
