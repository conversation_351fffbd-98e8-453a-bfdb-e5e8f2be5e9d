<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CourseEnrollment;
use App\Models\Student;
use App\Models\Course;
use Carbon\Carbon;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $students = Student::all();
        $courses = Course::all();

        // Nhóm courses theo level để tạo progression logic
        $coursesByLevel = $courses->groupBy('level');
        $levels = ['A1', 'A2', 'A3', 'TA 2/6'];

        foreach ($students as $student) {
            $this->createStudentEnrollmentProgression($student, $coursesByLevel, $levels);
        }
    }

    private function createStudentEnrollmentProgression($student, $coursesByLevel, $levels)
    {
        $currentLevel = 0; // Bắt đầu từ A1
        $hasFailures = rand(0, 1); // 50% chance có môn rớt

        // Mỗi student sẽ có progression qua các levels
        while ($currentLevel < count($levels) && $currentLevel < 3) { // Tối đa 3 levels
            $level = $levels[$currentLevel];
            $coursesInLevel = $coursesByLevel[$level] ?? collect();

            if ($coursesInLevel->isEmpty()) {
                $currentLevel++;
                continue;
            }

            // Chọn 1-2 courses trong level này
            $selectedCourses = $coursesInLevel->random(min(2, $coursesInLevel->count()));

            foreach ($selectedCourses as $course) {
                $enrollmentData = $this->determineEnrollmentStatus($currentLevel, $hasFailures);

                CourseEnrollment::create([
                    'student_id' => $student->student_id,
                    'assigned_course_id' => $course->course_id,
                    'status' => $enrollmentData['status'],
                    'registration_date' => $enrollmentData['registration_date'],
                ]);

                // Nếu rớt thì đăng ký lại course cùng level
                if ($enrollmentData['status'] == 4 && rand(0, 1)) { // 50% chance đăng ký lại
                    $retakeCourse = $coursesInLevel->where('course_id', '!=', $course->course_id)->first();
                    if ($retakeCourse) {
                        CourseEnrollment::create([
                            'student_id' => $student->student_id,
                            'assigned_course_id' => $retakeCourse->course_id,
                            'status' => rand(1, 2), // Pending hoặc studying
                            'registration_date' => Carbon::now()->subDays(rand(1, 30)),
                        ]);
                    }
                }
            }

            $currentLevel++;
        }

        // Thêm một số enrollments pending cho courses level cao hơn
        if ($currentLevel < count($levels)) {
            $nextLevel = $levels[$currentLevel];
            $nextCourses = $coursesByLevel[$nextLevel] ?? collect();
            if ($nextCourses->isNotEmpty() && rand(0, 1)) { // 50% chance
                $pendingCourse = $nextCourses->random();
                CourseEnrollment::create([
                    'student_id' => $student->student_id,
                    'assigned_course_id' => $pendingCourse->course_id,
                    'status' => 1, // Pending confirmation
                    'registration_date' => Carbon::now()->subDays(rand(1, 7)),
                ]);
            }
        }
    }

    private function determineEnrollmentStatus($levelIndex, $hasFailures)
    {
        // Logic phức tạp để tạo realistic enrollment patterns
        if ($levelIndex == 0) { // A1 level
            $statusOptions = [2, 2, 2, 3, 3]; // Mostly studying/passed
            if ($hasFailures) {
                $statusOptions[] = 4; // Add failure chance
            }
        } elseif ($levelIndex == 1) { // A2 level
            $statusOptions = [2, 2, 3, 3];
            if ($hasFailures) {
                $statusOptions[] = 4;
            }
        } else { // Higher levels
            $statusOptions = [1, 2, 2, 3];
        }

        $status = $statusOptions[array_rand($statusOptions)];

        // Determine registration date based on status
        switch ($status) {
            case 1: // Pending
                $registrationDate = Carbon::now()->subDays(rand(1, 14));
                break;
            case 2: // Studying
                $registrationDate = Carbon::now()->subDays(rand(15, 60));
                break;
            case 3: // Passed
                $registrationDate = Carbon::now()->subDays(rand(61, 180));
                break;
            case 4: // Failed
                $registrationDate = Carbon::now()->subDays(rand(61, 120));
                break;
            default:
                $registrationDate = Carbon::now()->subDays(rand(1, 90));
        }

        return [
            'status' => $status,
            'registration_date' => $registrationDate
        ];
    }
}
