<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CourseEnrollment;
use App\Models\Student;
use App\Models\Course;
use Carbon\Carbon;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $students = Student::all();
        $courses = Course::all();

        foreach ($students as $student) {
            $this->createSimpleEnrollments($student, $courses);
        }
    }

    private function createSimpleEnrollments($student, $courses)
    {
        // Mỗi student chỉ có 2-3 enrollments
        $maxEnrollments = rand(2, 3);

        // Chọn ngẫu nhiên courses
        $selectedCourses = $courses->random(min($maxEnrollments, $courses->count()));

        foreach ($selectedCourses as $course) {
            $enrollmentData = $this->determineEnrollmentStatus();

            CourseEnrollment::create([
                'student_id' => $student->student_id,
                'assigned_course_id' => $course->course_id,
                'status' => $enrollmentData['status'],
                'registration_date' => $enrollmentData['registration_date'],
            ]);
        }
    }

    private function determineEnrollmentStatus()
    {
        // Đơn giản hóa logic status
        $statusDistribution = [
            1 => 10, // 10% Pending
            2 => 40, // 40% Studying
            3 => 35, // 35% Passed
            4 => 15  // 15% Failed
        ];

        $random = rand(1, 100);
        $cumulative = 0;
        $status = 2; // Default studying

        foreach ($statusDistribution as $statusValue => $percentage) {
            $cumulative += $percentage;
            if ($random <= $cumulative) {
                $status = $statusValue;
                break;
            }
        }

        // Determine registration date based on status
        switch ($status) {
            case 1: // Pending
                $registrationDate = Carbon::now()->subDays(rand(1, 14));
                break;
            case 2: // Studying
                $registrationDate = Carbon::now()->subDays(rand(15, 60));
                break;
            case 3: // Passed
                $registrationDate = Carbon::now()->subDays(rand(61, 180));
                break;
            case 4: // Failed
                $registrationDate = Carbon::now()->subDays(rand(61, 120));
                break;
            default:
                $registrationDate = Carbon::now()->subDays(rand(1, 90));
        }

        return [
            'status' => $status,
            'registration_date' => $registrationDate
        ];
    }
}
