<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CourseEnrollment;
use App\Models\Student;
use App\Models\Course;
use Carbon\Carbon;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $students = Student::all();
        $courses = Course::all();

        // Nhóm courses theo level để tạo progression logic
        $coursesByLevel = $courses->groupBy('level');
        $levels = ['A1', 'A2', 'A3', 'TA 2/6'];

        foreach ($students as $student) {
            $this->createStudentEnrollmentProgression($student, $coursesByLevel, $levels);
        }
    }

    private function createStudentEnrollmentProgression($student, $coursesByLevel, $levels)
    {
        // Giảm số lượng enrollments: mỗi student chỉ có 2-3 enrollments
        $maxEnrollments = rand(2, 3);
        $enrollmentCount = 0;

        // Chọn ngẫu nhiên 1-2 levels để tham gia
        $numLevels = rand(1, 2);
        $selectedLevels = collect($levels)->random($numLevels);

        // Đảm bảo $selectedLevels luôn là array
        if ($numLevels == 1) {
            $selectedLevels = [$selectedLevels];
        } else {
            $selectedLevels = $selectedLevels->toArray();
        }

        foreach ($selectedLevels as $level) {
            if ($enrollmentCount >= $maxEnrollments) {
                break;
            }

            $coursesInLevel = $coursesByLevel->get($level, collect());
            if ($coursesInLevel->isEmpty()) {
                continue;
            }

            // Chỉ chọn 1 course trong mỗi level
            $selectedCourse = $coursesInLevel->random();

            $enrollmentData = $this->determineEnrollmentStatus($level);

            CourseEnrollment::create([
                'student_id' => $student->student_id,
                'assigned_course_id' => $selectedCourse->course_id,
                'status' => $enrollmentData['status'],
                'registration_date' => $enrollmentData['registration_date'],
            ]);

            $enrollmentCount++;

            // Chỉ 20% chance có enrollment thứ 2 trong cùng level (nếu rớt)
            if ($enrollmentData['status'] == 4 && rand(1, 100) <= 20 && $enrollmentCount < $maxEnrollments) {
                $retakeCourse = $coursesInLevel->where('course_id', '!=', $selectedCourse->course_id)->first();
                if ($retakeCourse) {
                    CourseEnrollment::create([
                        'student_id' => $student->student_id,
                        'assigned_course_id' => $retakeCourse->course_id,
                        'status' => rand(1, 2), // Pending hoặc studying
                        'registration_date' => Carbon::now()->subDays(rand(1, 30)),
                    ]);
                    $enrollmentCount++;
                }
            }
        }
    }

    private function determineEnrollmentStatus($level)
    {
        // Đơn giản hóa logic status
        $statusDistribution = [
            1 => 10, // 10% Pending
            2 => 40, // 40% Studying
            3 => 35, // 35% Passed
            4 => 15  // 15% Failed
        ];

        $random = rand(1, 100);
        $cumulative = 0;
        $status = 2; // Default studying

        foreach ($statusDistribution as $statusValue => $percentage) {
            $cumulative += $percentage;
            if ($random <= $cumulative) {
                $status = $statusValue;
                break;
            }
        }

        // Determine registration date based on status
        switch ($status) {
            case 1: // Pending
                $registrationDate = Carbon::now()->subDays(rand(1, 14));
                break;
            case 2: // Studying
                $registrationDate = Carbon::now()->subDays(rand(15, 60));
                break;
            case 3: // Passed
                $registrationDate = Carbon::now()->subDays(rand(61, 180));
                break;
            case 4: // Failed
                $registrationDate = Carbon::now()->subDays(rand(61, 120));
                break;
            default:
                $registrationDate = Carbon::now()->subDays(rand(1, 90));
        }

        return [
            'status' => $status,
            'registration_date' => $registrationDate
        ];
    }
}
