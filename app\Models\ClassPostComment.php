<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClassPostComment extends Model
{
    protected $fillable = [
        'comment_id',
        'post_id',
        'student_id',
        'teacher_id',
        'content',
        'status',
    ];

    protected $primaryKey = 'comment_id';

    // Đ<PERSON><PERSON> nghĩa các quan hệ với các model khác
    
    /**
     * Quan hệ với ClassPost
     */
    public function post()
    {
        return $this->belongsTo(ClassPost::class, 'post_id', 'post_id');
    }

    /**
     * <PERSON>uan hệ với Student
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'student_id');
    }

    /**
     * <PERSON>uan hệ với Teacher
     */
    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'teacher_id', 'teacher_id');
    }

    /**
     * Accessor để lấy tác giả (student hoặc teacher)
     */
    public function getAuthorAttribute()
    {
        if ($this->student_id) {
            return $this->student;
        } elseif ($this->teacher_id) {
            return $this->teacher;
        }
        return null;
    }

    /**
     * Scope để lấy các comment đang hoạt động
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope để lấy comment theo bài viết
     */
    public function scopeByPost($query, $postId)
    {
        return $query->where('post_id', $postId);
    }

    /**
     * Accessor để lấy tên tác giả
     */
    public function getAuthorNameAttribute()
    {
        $author = $this->getAuthorAttribute();
        if ($author) {
            return $author->fullname ?? $author->name ?? 'Unknown';
        }
        return 'Unknown';
    }

    /**
     * Accessor để lấy loại tác giả
     */
    public function getAuthorTypeAttribute()
    {
        if ($this->student_id) {
            return 'student';
        } elseif ($this->teacher_id) {
            return 'teacher';
        }
        return null;
    }
}
