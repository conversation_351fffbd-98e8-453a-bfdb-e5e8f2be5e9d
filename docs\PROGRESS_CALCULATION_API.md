# Progress Calculation API Documentation

## Overview
This document describes the updated progress calculation system that uses the CORRECT formula based on course_id instead of lesson-level calculations.

## New Formula
The progress calculation now uses the following SQL formula:

```sql
SELECT
    COALESCE((SUM(answered_count * question_count) / NULLIF(SUM(question_count * question_count), 0)) * 100, 0) AS course_progress,
    SUM(question_count) as total_questions,
    SUM(answered_count) as total_answered,
    SUM(correct_count) as total_correct
FROM (
    SELECT
        lpc.lesson_part_id,
        COUNT(DISTINCT q.questions_id) AS question_count,
        COUNT(DISTINCT CASE WHEN sa.questions_id IS NOT NULL THEN sa.questions_id END) AS answered_count,
        COUNT(DISTINCT CASE WHEN a.is_correct = 1 AND sa.answer_text = a.answer_text THEN sa.questions_id END) AS correct_count
    FROM lesson_part_contents lpc
    JOIN questions q ON lpc.contents_id = q.contents_id
    JOIN lesson_parts lp ON lpc.lesson_part_id = lp.lesson_part_id
    LEFT JOIN student_answers sa ON q.questions_id = sa.questions_id AND sa.student_id = ? AND sa.course_id = ?
    LEFT JOIN answers a ON q.questions_id = a.questions_id AND a.is_correct = 1
    WHERE lp.level = ?
    GROUP BY lpc.lesson_part_id
) AS progress_table
```

## Updated API Endpoints

### 1. Course Progress (Primary API)
**GET** `/api/progress/course/{courseId}/student/{studentId}`

**Response:**
```json
{
    "success": true,
    "data": {
        "student_id": 1,
        "course_id": 1,
        "course_name": "English A1",
        "course_level": "A1",
        "total_questions": 100,
        "answered_questions": 75,
        "correct_answers": 60,
        "progress_percentage": 75.0,
        "correct_percentage": 60.0,
        "is_completed": false,
        "required_correct_percentage": 70
    }
}
```

### 2. Lesson Part Progress (with Course Context)
**GET** `/api/progress/lesson-part/{lessonPartId}/student/{studentId}/course/{courseId}`

**Response:**
```json
{
    "success": true,
    "data": {
        "student_id": 1,
        "lesson_part_id": 1,
        "course_id": 1,
        "lesson_part_title": "Grammar",
        "total_questions": 10,
        "answered_questions": 8,
        "correct_answers": 7,
        "progress_percentage": 80.0,
        "is_completed": true,
        "required_correct_answers": 7
    }
}
```

### 3. Student Overall Progress
**GET** `/api/progress/student/{studentId}/overview`

**Response:**
```json
{
    "success": true,
    "data": {
        "student_id": 1,
        "student_name": "John Doe",
        "total_courses": 3,
        "completed_courses": 1,
        "overall_progress_percentage": 65.5,
        "courses_progress": [
            {
                "student_id": 1,
                "course_id": 1,
                "course_name": "English A1",
                "course_level": "A1",
                "total_questions": 100,
                "answered_questions": 75,
                "correct_answers": 60,
                "progress_percentage": 75.0,
                "correct_percentage": 60.0,
                "is_completed": false,
                "required_correct_percentage": 70
            }
        ]
    }
}
```

## Completion Criteria
- **Lesson Part Completion**: Student must answer all questions AND get at least 70% correct
- **Course Completion**: Student must achieve 70% correct answers across all lesson parts in the course

## Key Changes from Previous System
1. **Course-based calculation**: Progress is now calculated per course_id instead of lesson level
2. **Weighted formula**: Uses the correct mathematical formula for progress calculation
3. **Correct answer tracking**: Properly tracks correct answers by comparing with answer database
4. **70% threshold**: Consistent 70% threshold for completion across all levels

## Database Relationships
```
courses (course_id, level) 
  -> lessons (level) 
    -> lesson_parts (level) 
      -> lesson_part_contents 
        -> questions 
          -> student_answers (course_id, student_id)
          -> answers (is_correct)
```

## Updated Controllers
- `ProgressController`: Primary progress calculation with new formula
- `EnrollmentController`: Course enrollment progress calculation
- `StatisticsController`: Statistics using correct progress calculation
- `QuestionController`: Progress calculation for question submission
- `StudentProgressController`: Detailed progress tracking

## Migration Notes
- All existing APIs maintain backward compatibility
- New course-based APIs are recommended for new implementations
- Legacy lesson-level APIs still available but deprecated
