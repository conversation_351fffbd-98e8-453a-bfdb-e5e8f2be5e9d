# API Progress Tracking Documentation

## Tóm tắt các API đã cập nhật theo tài liệu

### 1. Student Progress APIs

#### 1.1 Cập nhật tiến độ học sinh
```
POST /api/student-progress
```
**Request Body:**
```json
{
    "score_id": 123,
    "completion_status": true,
    "completion_percentage": 85.5
}
```

#### 1.2 L<PERSON>y tiến độ khóa học của học sinh
```
GET /api/progress/course/{courseId}/student/{studentId}
```
**Response:**
```json
{
    "success": true,
    "data": {
        "course_id": 1,
        "course_name": "English Basic",
        "student_id": 123,
        "enrollment_status": 2,
        "overall_progress_percentage": 75.5,
        "lessons_progress": [
            {
                "level": 1,
                "lesson_title": "Grammar",
                "completed_parts": 1,
                "total_parts": 1,
                "progress_percentage": 100.0
            }
        ],
        "total_time_spent_minutes": 0,
        "estimated_completion_date": "2025-07-15"
    }
}
```

#### 1.3 Tổng quan tiến độ học sinh
```
GET /api/progress/student/{studentId}/overview
```
**Response:**
```json
{
    "student_id": 123,
    "total_courses": 3,
    "completed_courses": 1,
    "in_progress_courses": 2,
    "overall_progress_percentage": 65.33,
    "total_study_time_minutes": 450,
    "achievements_count": 1,
    "current_streak_days": 0
}
```

### 2. Enrollment Management APIs

#### 2.1 Lấy danh sách đăng ký của học sinh
```
GET /api/enrollments/student/{studentId}
```
**Response:**
```json
[
    {
        "enrollment_id": 1,
        "student_id": 123,
        "assigned_course_id": 1,
        "registration_date": "2025-06-01",
        "status": 2,
        "course": {
            "course_id": 1,
            "level": 1,
            "course_name": "English Basic",
            "description": "Basic English course",
            "status": 1
        },
        "progress_percentage": 75.5,
        "last_activity_date": "2025-06-20"
    }
]
```

#### 2.2 Cập nhật trạng thái đăng ký
```
PUT /api/enrollments/{enrollmentId}/status
```
**Request Body:**
```json
{
    "status": 3,
    "notes": "Completed successfully"
}
```

### 3. Course Statistics API

#### 3.1 Thống kê số lượng học sinh theo khóa học
```
GET /api/courses/{courseId}/students/count
```
**Response:**
```json
{
    "course_id": 1,
    "course_name": "English Basic",
    "total_students": 50,
    "active_students": 30,
    "completed_students": 15,
    "pending_students": 3,
    "failed_students": 2,
    "completion_rate": 30.0
}
```

### 4. Question & Score APIs (Cập nhật)

#### 4.1 Nộp điểm lesson part (đã cập nhật)
```
POST /api/lesson-part-scores
```
**Request Body:**
```json
{
    "student_id": 123,
    "lesson_part_id": 1,
    "course_id": 1,
    "attempt_no": 1,
    "score": 85.5,
    "total_questions": 10,
    "correct_answers": 8
}
```
**Response:**
```json
{
    "success": true,
    "message": "Score submitted successfully",
    "score_data": {
        "score_id": 456,
        "score": 85.5,
        "attempt_no": 1,
        "submit_time": "2025-06-21T10:30:00Z",
        "completion_percentage": 80.0
    },
    "progress_updated": true,
    "is_completed": true,
    "course_progress_percentage": 75.5
}
```

### 5. Statistics & Analytics APIs

#### 5.1 Thống kê tổng quan
```
GET /api/statistics/overview
```
**Response:**
```json
{
    "overview": {
        "total_students": 500,
        "total_courses": 25,
        "total_enrollments": 1200,
        "total_questions": 5000,
        "students_with_progress": 450,
        "average_completion_rate": 68.5
    },
    "enrollment_status": {
        "pending": 50,
        "active": 800,
        "completed": 300,
        "failed": 50
    },
    "course_levels": {
        "1": 10,
        "2": 8,
        "3": 7
    },
    "recent_activity": {
        "answers_last_7_days": 2500,
        "scores_last_7_days": 150
    }
}
```

#### 5.2 Thống kê khóa học chi tiết
```
GET /api/statistics/courses
```

#### 5.3 Thống kê hiệu suất học sinh
```
GET /api/statistics/students/performance
```

## Cách tính PROGRESS theo tài liệu

### 1. Progress Lesson Part
- **Công thức:** `(số câu đã trả lời / tổng số câu) * 100`
- **Hoàn thành:** >= 70% số câu trả lời đúng
- **Cập nhật:** Mỗi khi học sinh nộp bài

### 2. Progress Course
- **Công thức:** Trung bình progress của tất cả lesson parts trong course level
- **Tính toán:** 
  ```
  Tổng progress các lesson parts / Số lượng lesson parts
  ```

### 3. Progress Overall Student
- **Công thức:** Trung bình progress của tất cả courses đã đăng ký
- **Bao gồm:** Tất cả enrollment status >= 1

### 4. Enrollment Status
- **1:** Chờ xác nhận (pending confirmation)
- **2:** Đang học (studying)
- **3:** Đạt (passed)
- **4:** Không đạt (failed)

### 5. Completion Criteria
- **Lesson Part:** >= 70% câu trả lời đúng
- **Course:** Hoàn thành tất cả lesson parts trong level
- **Overall:** Dựa trên enrollment status

## Lưu ý quan trọng

1. **Real-time Updates:** Progress được cập nhật ngay khi học sinh nộp bài
2. **Data Consistency:** Sử dụng transactions khi cập nhật multiple tables
3. **Performance:** Cache kết quả tính toán cho các queries phức tạp
4. **Error Handling:** Graceful degradation khi không tính được progress
5. **Validation:** Kiểm tra dữ liệu đầu vào trước khi tính toán

## Testing Recommendations

1. Test với dữ liệu thực tế từ database
2. Kiểm tra performance với large datasets
3. Validate tính chính xác của các công thức tính toán
4. Test error scenarios và edge cases
