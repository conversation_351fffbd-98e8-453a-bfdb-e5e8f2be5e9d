<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
   
          $this->call([
            AdminSeeder::class,
            StudentSeeder::class,
            TeacherSeeder::class,
            LessonSeeder::class,
            CourseSeeder::class,
            LessonPartSeeder::class,

            EnhancedQuestionsSeeder::class, // Seeder cũ để test trước
            CourseEnrollmentSeeder::class,
            TeacherCourseAssignmentSeeder::class,
            RealisticStudentAnswerSeeder::class, // Seeder mới tạo student answers realistic
            ExamResultSeeder::class, // Updated để tạo exam results realistic
            LessonPartScoreSeeder::class,
            StudentProgressSeeder::class,
            StudentEvaluationSeeder::class,
            ClassPostSeeder::class,
            ClassPostCommentSeeder::class,
            NotificationSeeder::class,
        ]);
                      
    }
}
