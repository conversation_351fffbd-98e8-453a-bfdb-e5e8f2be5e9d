<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
   
          $this->call([
            AdminSeeder::class,
            StudentSeeder::class,
            TeacherSeeder::class,
            LessonSeeder::class,
            CourseSeeder::class,
            LessonPartSeeder::class,

            ComprehensiveQuestionsSeeder::class, // Seeder mới với 10 câu hỏi mỗi lesson part, đã tối ưu
            CourseEnrollmentSeeder::class,
            TeacherCourseAssignmentSeeder::class,
            StudentAnswerSeeder::class, // Sử dụng seeder cũ vì seeder mới có vấn đề
            ExamResultSeeder::class, // Updated để tạo exam results realistic
            // LessonPartScoreSeeder::class, // Comment out để test
            // StudentProgressSeeder::class, // Comment out để test
            // StudentEvaluationSeeder::class, // Comment out để test
            // ClassPostSeeder::class, // Comment out để test
            // ClassPostCommentSeeder::class, // Comment out để test
            // NotificationSeeder::class, // Comment out để test
        ]);
                      
    }
}
