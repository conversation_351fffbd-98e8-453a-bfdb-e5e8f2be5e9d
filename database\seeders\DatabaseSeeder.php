<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
   
          $this->call([
            AdminSeeder::class,
            StudentSeeder::class,
            TeacherSeeder::class,
            LessonSeeder::class,
            CourseSeeder::class,
            LessonPartSeeder::class,

            EnhancedQuestionsSeeder::class, // Sử dụng seeder cũ vì seeder mới có vấn đề performance
            CourseEnrollmentSeeder::class,
            TeacherCourseAssignmentSeeder::class,
            RealisticStudentAnswerSeeder::class, // Seeder mới tạo student answers realistic
            ExamResultSeeder::class, // Updated để tạo exam results realistic
            // LessonPartScoreSeeder::class, // Comment out để test
            // StudentProgressSeeder::class, // Comment out để test
            // StudentEvaluationSeeder::class, // Comment out để test
            // ClassPostSeeder::class, // Comment out để test
            // ClassPostCommentSeeder::class, // Comment out để test
            // NotificationSeeder::class, // Comment out để test
        ]);
                      
    }
}
