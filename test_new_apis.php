<?php

echo "🚀 Testing New APIs\n";
echo "===================\n\n";

function testAPI($url, $description, $method = 'GET', $data = null) {
    echo "Testing: $description\n";
    echo "Method: $method\n";
    echo "URL: $url\n";
    if ($data) {
        echo "Data: " . json_encode($data) . "\n";
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json', 'Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode >= 200 && $httpCode < 300) {
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "✅ SUCCESS\n";
            if (isset($responseData['data'])) {
                $count = is_array($responseData['data']) ? count($responseData['data']) : 'single object';
                echo "Data count: $count\n";
            }
            if (isset($responseData['message'])) {
                echo "Message: " . $responseData['message'] . "\n";
            }
        } else {
            echo "❌ FAILED - Invalid JSON\n";
        }
    } else {
        echo "❌ FAILED\n";
        echo "Response: " . substr($response, 0, 300) . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    return $httpCode >= 200 && $httpCode < 300;
}

$baseUrl = 'http://localhost:8000/api';

echo "=== API 1: Smart Course Registration ===\n";
testAPI(
    "$baseUrl/enrollments/smart-register/student/5", 
    "Smart Course Registration for Student 5",
    'POST',
    [
        'level' => 'A1',
        'schedule' => 'Thứ 2-4-6 buổi sáng'
    ]
);

echo "=== API 2: Update Student Answers by Course and Lesson Part ===\n";
testAPI(
    "$baseUrl/student-answers/student/1/course/9/lesson-part/1", 
    "Update Student Answers for Student 1, Course 9, Lesson Part 1",
    'PUT',
    [
        'answers' => [
            [
                'question_id' => 1,
                'answer_text' => 'Test answer 1',
                'is_correct' => true
            ],
            [
                'question_id' => 2,
                'answer_text' => 'Test answer 2',
                'is_correct' => false
            ]
        ]
    ]
);

echo "=== API 3: Get Recent Submission Score and Progress ===\n";
$submissionTime = date('Y-m-d H:i:s', strtotime('-1 hour'));
testAPI(
    "$baseUrl/student-answers/recent-submission/student/1/course/9/lesson-part/1?submission_time=" . urlencode($submissionTime), 
    "Get Recent Submission for Student 1, Course 9, Lesson Part 1"
);

echo "=== Testing with different parameters ===\n";

echo "=== API 1b: Smart Registration with A2 level ===\n";
testAPI(
    "$baseUrl/enrollments/smart-register/student/6", 
    "Smart Course Registration for Student 6 - A2 Level",
    'POST',
    [
        'level' => 'A2',
        'schedule' => 'Thứ 3-5-7 buổi chiều'
    ]
);

echo "=== API 1c: Smart Registration with invalid schedule ===\n";
testAPI(
    "$baseUrl/enrollments/smart-register/student/7", 
    "Smart Course Registration with Invalid Schedule",
    'POST',
    [
        'level' => 'A1',
        'schedule' => 'Thứ 8-9-10 buổi đêm'
    ]
);

echo "🏁 Testing completed!\n";
